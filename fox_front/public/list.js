function init(){
    console.log('init');

// 加载外部脚本
function loadScript(src) {
    return new Promise((resolve, reject) => {
        const script = document.createElement('script');
        script.src = src;
        script.onload = resolve;
        script.onerror = reject;
        document.head.appendChild(script);
    });
}

// 异步加载所需的脚本
Promise.all([
    loadScript('https://cdnjs.cloudflare.com/ajax/libs/layer/3.5.1/layer.js'),
    loadScript('https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js'),
    loadScript('https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js')
]).then(() => {
    createButtons();
}).catch(error => {
    console.error('Failed to load scripts:', error);
});

/**
 * 创建加载提示
 * @param {string} msg 提示信息
 * @returns {number} loading实例的索引
 */
function setLoading(msg) {
    return layer.msg(msg, {
        icon: 16,
        shade: 0.3,
        time: 0 // 不自动关闭
    });
}
// 获取当前聊天记录的标题
function getCurrentTitle() {
    // 尝试获取标题元素
    const titleElement = document.getElementsByClassName('text-gray-400')[0];
    
    if (!titleElement) {
        return null;
    }
    
    // 获取文本内容并处理
    const titleText = titleElement.innerText.trim();
    
    // 确保标题有效
    return titleText || null;
    
    // 可以添加更多的标题处理逻辑
    // 比如长度限制、非法字符过滤等
}
/**
 * 导出对话为图片
 */
function export2Image() {
    // 创建加载提示
    const loading = setLoading("开始将对话导出图片,请稍后...");
    
    // 检查是否存在可导出的内容
    if (!document.getElementsByClassName("markdown").length) {
        layer.msg("未找到聊天记录, 无法导出图片, 请先选择一个聊天记录");
        return;
    }
    
    // 获取需要导出的 DOM 元素
    const articles = document.getElementsByTagName('article');
    const element = document.createElement('div');
    for (const article of articles) {
        element.appendChild(article.cloneNode(true));
    }
    document.body.appendChild(element);
    // 使用 html2canvas 替代 domtoimage
    html2canvas(element, {
        scale: 2,  // 设置缩放比例提高质量
        backgroundColor: document.documentElement.style.colorScheme === "dark" ? "#212121" : "white",
        useCORS: true,  // 允许加载跨域图片
        logging: false  // 关闭日志
    }).then(canvas => {
        document.body.removeChild(element);
        // 将 canvas 转换为 blob
        canvas.toBlob(blob => {
            const downloadLink = document.createElement("a");
            downloadLink.href = URL.createObjectURL(blob);
            downloadLink.download = (getCurrentTitle() || "聊天记录") + ".png";
            
            document.body.appendChild(downloadLink);
            downloadLink.click();
            document.body.removeChild(downloadLink);
            
            layer.close(loading);
        }, 'image/png');
    }).catch(error => {
        layer.msg("生成图片时出错");
        console.error("生成图片时出错", error);
        layer.close(loading);
    });
}

/**
 * 导出对话为PDF
 */
function export2PDF() {
    const loading = setLoading("正在导出PDF,请稍后...");
    
    if (!document.getElementsByClassName("markdown").length) {
        layer.msg("未找到聊天记录, 无法导出PDF");
        return;
    }

    // 创建临时容器
    const element = document.createElement('div');
    const articles = document.getElementsByTagName('article');
    for (const article of articles) {
        element.appendChild(article.cloneNode(true));
    }
    document.body.appendChild(element);

    // 配置PDF选项
    const opt = {
        margin: 1,
        filename: (getCurrentTitle() || "聊天记录") + '.pdf',
        image: { type: 'jpeg', quality: 0.98 },
        html2canvas: { 
            scale: 2,
            backgroundColor: document.documentElement.style.colorScheme === "dark" ? "#212121" : "white",
            useCORS: true
        },
        jsPDF: { unit: 'in', format: 'a4', orientation: 'portrait' }
    };

    // 使用html2pdf库导出PDF
    html2pdf().set(opt).from(element).save().then(() => {
        document.body.removeChild(element);
        layer.close(loading);
    }).catch(error => {
        layer.msg("生成PDF时出错");
        console.error("生成PDF时出错", error);
        layer.close(loading);
    });
}

/**
 * 自动换车功能
 */
function selectAnotherCar() {
    const loading = setLoading("正在自动换车，请稍后...");
    const auth = localStorage.getItem('token');
    // 发送GET请求到后端接口
    fetch('/api/chatGpt/car/selectAnotherCar', {
        method: 'GET',
        credentials: 'include', // 包含cookies
        headers: {
            'Content-Type': 'application/json',
            'Authorization': auth
        }
    })
    .then(response => response.json())
    .then(async data => {
        // 判断是否成功获取到carid
        if (data && data.data && data.data.carid) {
            const carId = data.data.carid;
            layer.msg("🚗 换车成功！新车ID: " + carId + "，正在登录...", {
                icon: 1,
                time: 2000
            });
            
            try {
                // 执行登录操作
                const userToken = localStorage.getItem('userToken') || localStorage.getItem('token') || '';
                
                const formData = new FormData();
                formData.append('usertoken', userToken);
                formData.append('action', 'default');
                
                const loginResponse = await fetch('/auth/login?carid=' + carId, {
                    method: 'POST',
                    credentials: 'include',
                    body: formData
                });
                
                layer.close(loading);
                
                if (loginResponse.status == 200) {
                    layer.msg("✅ 换车并登录成功！即将跳转到首页", {
                        icon: 1,
                        time: 2000
                    });
                    
                    setTimeout(() => {
                        window.location.href = '/';
                    }, 1000);
                } else {
                    layer.msg("❌ 登录失败，登录状态码: " + loginResponse.status, {
                        icon: 2,
                        time: 3000
                    });
                    console.log('登录响应:', loginResponse);
                }
                
            } catch (loginError) {
                layer.close(loading);
                layer.msg("❌ 登录过程出错，请手动刷新页面", {
                    icon: 2,
                    time: 3000
                });
                console.error('登录过程出错:', loginError);
            }
            
        } else {
            layer.close(loading);
            layer.msg("❌ 换车失败，请稍后重试", {
                icon: 2,
                time: 3000
            });
        }
    })
    .catch(error => {
        layer.close(loading);
        layer.msg("❌ 网络错误，换车失败", {
            icon: 2,
            time: 3000
        });
        console.error('换车请求失败:', error);
    });
}

/**
 * 创建抽屉式按钮组
 */
function createButtons() {
    // 检测是否为移动设备
    const isMobile = window.innerWidth <= 768;
    const isLandscape = isMobile && window.innerWidth > window.innerHeight;
    
    // 创建主容器
    const containerStyle = {
        "position": "fixed",
        "right": isMobile ? "15px" : "20px",
        "z-index": "1000"
    };
    
    // 移动端放在右上角，桌面端保持右下角
    if (isMobile) {
        containerStyle.top = "80px"; // 从 20px 改为 80px，往下移动
        // 为移动端刘海屏等安全区域预留空间
        containerStyle.paddingTop = "env(safe-area-inset-top, 0px)";
    } else {
        containerStyle.bottom = "80px";
    }
    
    // 横屏时的特殊处理
    if (isLandscape) {
        containerStyle.right = "10px";
        if (isMobile) {
            containerStyle.top = "60px"; // 横屏时也相应调整
        } else {
            containerStyle.bottom = "15px";
        }
    }
    
    const $buttonContainer = $("<div id='buttonContainer'></div>").css(containerStyle);

    // 创建触发按钮（主按钮）
    const $triggerButton = $("<button id='drawerTrigger'>⚡</button>").css({
        "width": "50px",
        "height": "50px",
        "border-radius": "50%",
        "background": "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
        "color": "#ffffff",
        "font-size": "20px",
        "border": "none",
        "cursor": "pointer",
        "box-shadow": "0 4px 12px rgba(102, 126, 234, 0.4)",
        "transition": "all 0.3s ease",
        "display": "flex",
        "align-items": "center",
        "justify-content": "center",
        "position": "relative",
        "z-index": "1001"
    });

    // 创建抽屉容器（功能按钮容器）
    const drawerPosition = isMobile ? "top" : "bottom";
    const drawerOffset = "60px";
    
    const $drawer = $("<div id='buttonDrawer'></div>").css({
        "position": "absolute",
        [drawerPosition]: drawerOffset, // 移动端在按钮下方，桌面端在按钮上方
        "right": "0",
        "display": "flex",
        "flex-direction": "column",
        "gap": "8px",
        "opacity": "0",
        "transform": isMobile ? "translateY(-20px)" : "translateY(20px)", // 移动端向上收起，桌面端向下收起
        "transition": "all 0.3s ease",
        "pointer-events": "none" // 默认不可点击
    });

    // 抽屉内按钮样式（保持较小尺寸）
    const drawerButtonStyle = {
        "padding": "8px 12px",
        "border-radius": "12px",
        "background": "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
        "color": "#ffffff",
        "font-size": "12px",
        "font-weight": "500",
        "cursor": "pointer",
        "transition": "all 0.3s ease",
        "border": "none",
        "box-shadow": "0 2px 8px rgba(102, 126, 234, 0.3)",
        "min-width": "90px",
        "text-align": "center",
        "white-space": "nowrap",
        "transform": "scale(0.8)",
        "opacity": "0"
    };

    // 创建抽屉内的功能按钮
    const $goHomeBtn = $("<button>🏠 返回首页</button>")
        .css(drawerButtonStyle)
        .click(() => {
            window.location.href = "/list";
            toggleDrawer(false); // 执行后收起抽屉
        });

    const $exportImgBtn = $("<button>📷 导出图片</button>")
        .css(drawerButtonStyle)
        .click(() => {
            export2Image();
            toggleDrawer(false);
        });

    const $exportPdfBtn = $("<button>📄 导出PDF</button>")
        .css(drawerButtonStyle)
        .click(() => {
            export2PDF();
            toggleDrawer(false);
        });

    const $selectCarBtn = $("<button>🚗 自动换车</button>")
        .css(drawerButtonStyle)
        .click(() => {
            selectAnotherCar();
            toggleDrawer(false);
        });

    // 将功能按钮添加到抽屉中
    $drawer
        .append($goHomeBtn)
        .append($selectCarBtn)
        .append($exportImgBtn)
        .append($exportPdfBtn);

    // 组装整个按钮容器
    $buttonContainer
        .append($drawer)
        .append($triggerButton);

    // 将容器添加到页面
    $("body").append($buttonContainer);
    console.log('append buttonContainer')
    
    // 抽屉状态管理
    let isDrawerOpen = false;
    
    // 抽屉切换函数
    function toggleDrawer(forceState = null) {
        const shouldOpen = forceState !== null ? forceState : !isDrawerOpen;
        const openTransform = 'translateY(0)';
        const closeTransform = isMobile ? 'translateY(-20px)' : 'translateY(20px)';
        
        if (shouldOpen) {
            // 展开抽屉
            $drawer.css({
                'opacity': '1',
                'transform': openTransform,
                'pointer-events': 'auto'
            });
            
            // 按钮依次出现动画
            $drawer.find('button').each(function(index) {
                const $btn = $(this);
                setTimeout(() => {
                    $btn.css({
                        'transform': 'scale(1)',
                        'opacity': '1'
                    });
                }, index * 100);
            });
            
            // 触发按钮旋转
            $triggerButton.css('transform', 'rotate(45deg)');
            isDrawerOpen = true;
        } else {
            // 收起抽屉
            $drawer.css({
                'opacity': '0',
                'transform': closeTransform,
                'pointer-events': 'none'
            });
            
            // 重置按钮状态
            $drawer.find('button').css({
                'transform': 'scale(0.8)',
                'opacity': '0'
            });
            
            // 触发按钮复位
            $triggerButton.css('transform', 'rotate(0deg)');
            isDrawerOpen = false;
        }
    }
    
    // 触发按钮点击事件
    $triggerButton.click(function(e) {
        e.stopPropagation();
        toggleDrawer();
    });
    
    // 点击页面其他地方收起抽屉
    $(document).on('click', function(e) {
        if (isDrawerOpen && !$(e.target).closest('#buttonContainer').length) {
            toggleDrawer(false);
        }
    });
    
    // 添加触发按钮的悬停/触摸效果
    if (isMobile) {
        $triggerButton.on('touchstart', function() {
            const currentRotate = isDrawerOpen ? 'rotate(45deg)' : 'rotate(0deg)';
            $(this).css({
                'transform': currentRotate + ' scale(0.9)',
                'box-shadow': '0 2px 8px rgba(102, 126, 234, 0.6)'
            });
        }).on('touchend touchcancel', function() {
            const currentRotate = isDrawerOpen ? 'rotate(45deg)' : 'rotate(0deg)';
            $(this).css({
                'transform': currentRotate,
                'box-shadow': '0 4px 12px rgba(102, 126, 234, 0.4)'
            });
        });
    } else {
        $triggerButton.hover(
            function() {
                const currentRotate = isDrawerOpen ? 'rotate(45deg)' : 'rotate(0deg)';
                $(this).css({
                    'box-shadow': '0 6px 16px rgba(102, 126, 234, 0.5)',
                    'transform': currentRotate + ' scale(1.1)'
                });
            },
            function() {
                const currentRotate = isDrawerOpen ? 'rotate(45deg)' : 'rotate(0deg)';
                $(this).css({
                    'box-shadow': '0 4px 12px rgba(102, 126, 234, 0.4)',
                    'transform': currentRotate
                });
            }
        );
    }
    
    // 抽屉内按钮的悬停效果
    $drawer.find('button').hover(
        function() {
            $(this).css({
                'background': 'linear-gradient(135deg, #764ba2 0%, #667eea 100%)',
                'transform': 'scale(1.05)',
                'box-shadow': '0 4px 12px rgba(102, 126, 234, 0.4)'
            });
        },
        function() {
            $(this).css({
                'background': 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                'transform': 'scale(1)',
                'box-shadow': '0 2px 8px rgba(102, 126, 234, 0.3)'
            });
        }
    );
    
    // 添加窗口大小变化监听
    let resizeTimeout;
    $(window).on('resize', function() {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(function() {
            const newIsMobile = window.innerWidth <= 768;
            if (newIsMobile !== isMobile) {
                $('#buttonContainer').remove();
                createButtons();
            }
        }, 250);
    });
}
}
window.addEventListener('load', function() {
    console.log('load');
    setTimeout(() => {
        init();
    }, 1000);
});