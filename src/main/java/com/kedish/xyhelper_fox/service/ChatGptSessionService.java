package com.kedish.xyhelper_fox.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kedish.xyhelper_fox.cache.LocalCache;
import com.kedish.xyhelper_fox.component.ChatGptSessionAccessComponent;
import com.kedish.xyhelper_fox.component.ChatShareServerProxy;
import com.kedish.xyhelper_fox.component.ClaudeProxy;
import com.kedish.xyhelper_fox.exception.FoxException;
import com.kedish.xyhelper_fox.model.req.AddGptSessionReq;
import com.kedish.xyhelper_fox.model.req.PageQueryReq;
import com.kedish.xyhelper_fox.model.resp.Claude<PERSON>ar;
import com.kedish.xyhelper_fox.model.resp.GptCar;
import com.kedish.xyhelper_fox.model.resp.GrokCar;
import com.kedish.xyhelper_fox.repo.mapper.ChatGptSessionMapper;
import com.kedish.xyhelper_fox.repo.mapper.ClaudeSessionMapper;
import com.kedish.xyhelper_fox.repo.model.ChatGptSession;
import com.kedish.xyhelper_fox.repo.model.ChatgptUser;
import com.kedish.xyhelper_fox.repo.model.ClaudeSession;
import com.kedish.xyhelper_fox.repo.model.UserGroup;
import com.kedish.xyhelper_fox.security.UserContext;
import com.kedish.xyhelper_fox.utils.GptCarGenerator;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBatch;
import org.redisson.api.RFuture;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.NumberUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ChatGptSessionService {

    @Resource
    private ChatGptSessionMapper chatGptSessionMapper;

    @Resource
    private ChatGptConfigService chatGptConfigService;

    @Resource
    private ThreadPoolExecutor threadPoolExecutor;

    private volatile List<GptCar> nodeFree = new ArrayList<>();
    private volatile List<GptCar> node4o = new ArrayList<>();

    private volatile List<GptCar> nodePlus = new ArrayList<>();

    private volatile List<GptCar> nodeVirtualPlus = new ArrayList<>();

    private volatile List<GptCar> nodeTeam = new ArrayList<>();

    private volatile List<GptCar> nodeVirtualTeam = new ArrayList<>();

    private volatile List<GptCar> nodePro = new ArrayList<>();

    private volatile List<GptCar> nodeVirtualPro = new ArrayList<>();

    private volatile List<ClaudeCar> nodeClaude = new ArrayList<>();

    private volatile List<ClaudeCar> nodeVirtualClaude = new ArrayList<>();

    private volatile List<GrokCar> nodeGrok = new ArrayList<>();

    private volatile List<GrokCar> nodeVirtualGrok = new ArrayList<>();

    @Resource
    private LocalCache localCache;

    @Resource
    private RedissonClient redissonClient;
    private Map<String, GptCarGenerator.HeatConfig>
            heatConfigMap = new HashMap<>();
    @Resource
    private ChatShareServerProxy chatShareServerProxy;

    @Resource
    private ClaudeSessionMapper claudeSessionMapper;
    @Resource
    private ChatGptSessionAccessComponent chatGptSessionAccessComponent;

    @Resource
    private UserGroupService userGroupService;

    @Resource
    private ClaudeProxy claudeProxy;

    private volatile String currentHeat;

    @PostConstruct
    public void init() {
        GptCarGenerator.HeatConfig lowHeat = new GptCarGenerator.HeatConfig(80, 15, 5);
        GptCarGenerator.HeatConfig mediumHeat = new GptCarGenerator.HeatConfig(30, 60, 10);
        GptCarGenerator.HeatConfig highHeat = new GptCarGenerator.HeatConfig(10, 10, 80);
        heatConfigMap.put("low", lowHeat);
        heatConfigMap.put("medium", mediumHeat);
        heatConfigMap.put("high", highHeat);


        try {
            generateVirtualPlus();
            generateVirtualTeam();
            generateVirtualPro();
            generateVirtualClaude();
            generateVirtualGrok();
        } catch (Exception e) {
            log.error("generate virtual error", e);
        }

        refreshNode();
    }


    public synchronized void applyVirtualPlusSizeChange() {
        int virtualPlusSize = NumberUtils.parseNumber(localCache.getConfigMap().get("virtualPlusSize"), Integer.class);
        if (virtualPlusSize == 0) {
            nodeVirtualPlus.clear();
            log.info("虚拟Plus数量为0，已清空");
            nodePlus.removeIf(GptCar::isVirtual);
            return;
        }
        int newVirtualPlusSize = virtualPlusSize - nodeVirtualPlus.size();
        if (newVirtualPlusSize == 0) {

            return;
        }
        if (newVirtualPlusSize > 0) {
            Map<String, String> configMap = localCache.getConfigMap();
            List<GptCar> gptCars = GptCarGenerator.generateGptCars(newVirtualPlusSize,
                    heatConfigMap.get(configMap.getOrDefault("virtualPlusHeat", "high")), "PLUS");
            nodeVirtualPlus.addAll(gptCars);
            nodePlus.removeIf(GptCar::isVirtual);
            nodePlus.addAll(nodeVirtualPlus);
            log.info("虚拟Plus数量为{}，已生成{}个", virtualPlusSize, newVirtualPlusSize);
        } else {
            int removeCount = Math.abs(newVirtualPlusSize);
            if (removeCount > nodeVirtualPlus.size()) {
                removeCount = nodeVirtualPlus.size();
            }
            //随机移除
            for (int i = 0; i < removeCount; i++) {
                GptCar gptCar = randomPickOne(nodeVirtualPlus);
                nodeVirtualPlus.remove(gptCar);
            }
            nodePlus.removeIf(GptCar::isVirtual);
            nodePlus.addAll(nodeVirtualPlus);
            log.info("虚拟Plus数量为{}，已移除{}个", virtualPlusSize, removeCount);
        }
    }

    public synchronized void applyVirtualPlusHeatChange() {

        String s = localCache.getConfigMap().get("virtualPlusHeat");
        if (s == null) {
            s = "high";
        }
        if (s.equals(currentHeat)) {
            log.info("虚拟Plus热力配置未变化，不执行更新");
            return;
        }
        currentHeat = s;
        GptCarGenerator.HeatConfig heatConfig = heatConfigMap.get(s);
        GptCarGenerator.updateHeat(nodeVirtualPlus, heatConfig);
        log.info("虚拟Plus热力配置已更新为{}", s);
    }

    public synchronized void applyFreeSizeChange() {
        int freeSize = NumberUtils.parseNumber(localCache.getConfigMap().get("nodeFreeSize"), Integer.class);
        if (freeSize == 0) {
            nodeFree.clear();
            return;
        }
        nodeFree = node4o.stream().sorted(Comparator.comparing(GptCar::getCarID))
                .limit(freeSize).toList();
    }

    public synchronized void applyVirtualTeamSizeChange() {
        int virtualTeamSize = NumberUtils.parseNumber(localCache.getConfigMap().get("virtualTeamSize"), Integer.class);
        if (virtualTeamSize == 0) {
            nodeVirtualTeam.clear();
            log.info("虚拟Team数量为0，已清空");
            nodeTeam.removeIf(GptCar::isVirtual);
            return;
        }
        int newVirtualTeamSize = virtualTeamSize - nodeVirtualTeam.size();
        if (newVirtualTeamSize == 0) {
            return;
        }
        if (newVirtualTeamSize > 0) {
            Map<String, String> configMap = localCache.getConfigMap();
            List<GptCar> gptCars = GptCarGenerator.generateGptCars(newVirtualTeamSize,
                    heatConfigMap.get(configMap.getOrDefault("virtualPlusHeat", "high")), "TEAM");
            nodeVirtualTeam.addAll(gptCars);
            nodeTeam.removeIf(GptCar::isVirtual);
            nodeTeam.addAll(nodeVirtualTeam);
            log.info("虚拟Team数量为{}，已生成{}个", virtualTeamSize, newVirtualTeamSize);
        } else {
            int removeCount = Math.abs(newVirtualTeamSize);
            if (removeCount > nodeVirtualTeam.size()) {
                removeCount = nodeVirtualTeam.size();
            }
            //随机移除
            for (int i = 0; i < removeCount; i++) {
                GptCar gptCar = randomPickOne(nodeVirtualTeam);
                nodeVirtualTeam.remove(gptCar);
            }
            nodeTeam.removeIf(GptCar::isVirtual);
            nodeTeam.addAll(nodeVirtualTeam);
            log.info("虚拟Team数量为{}，已移除{}个", virtualTeamSize, removeCount);
        }
    }

    public synchronized void applyVirtualProSizeChange() {
        int virtualProSize = NumberUtils.parseNumber(localCache.getConfigMap().get("virtualProSize"), Integer.class);
        if (virtualProSize == 0) {
            nodeVirtualPro.clear();
            log.info("虚拟Pro数量为0，已清空");
            nodePro.removeIf(GptCar::isVirtual);
            return;
        }
        int newVirtualProSize = virtualProSize - nodeVirtualPro.size();
        if (newVirtualProSize == 0) {
            return;
        }
        if (newVirtualProSize > 0) {
            Map<String, String> configMap = localCache.getConfigMap();
            List<GptCar> gptCars = GptCarGenerator.generateGptCars(newVirtualProSize,
                    heatConfigMap.get(configMap.getOrDefault("virtualPlusHeat", "high")), "PRO");
            nodeVirtualPro.addAll(gptCars);
            nodePro.removeIf(GptCar::isVirtual);
            nodePro.addAll(nodeVirtualPro);
            log.info("虚拟Pro数量为{}，已生成{}个", virtualProSize, newVirtualProSize);
        } else {
            int removeCount = Math.abs(newVirtualProSize);
            if (removeCount > nodeVirtualPro.size()) {
                removeCount = nodeVirtualPro.size();
            }
            //随机移除
            for (int i = 0; i < removeCount; i++) {
                GptCar gptCar = randomPickOne(nodeVirtualPro);
                nodeVirtualPro.remove(gptCar);
            }
            nodePro.removeIf(GptCar::isVirtual);
            nodePro.addAll(nodeVirtualPro);
            log.info("虚拟Pro数量为{}，已移除{}个", virtualProSize, removeCount);
        }
    }

    private void generateVirtualPlus() {


        Map<String, String> configMap = chatGptConfigService.getConfigMap(Arrays.asList("virtualPlusSize", "virtualPlusHeat"));
        String heat = "medium";
        int virtualPlusSize = 0;
        if (configMap.containsKey("virtualPlusSize")) {
            try {

                virtualPlusSize = NumberUtils.parseNumber(configMap.get("virtualPlusSize"), Integer.class);
            } catch (Exception e) {
                log.error("generateVirtualPlus number format error", e);
                return;
            }
        }
        if (configMap.containsKey("virtualPlusHeat")) {
            heat = configMap.get("virtualPlusHeat");
        }
        currentHeat = heat;
        if (virtualPlusSize > 0) {

            List<GptCar> gptCars = GptCarGenerator.generateGptCars(virtualPlusSize, heatConfigMap.get(heat), "PLUS");
            nodeVirtualPlus = gptCars;
        }
    }

    private void generateVirtualTeam() {
        Map<String, String> configMap = chatGptConfigService.getConfigMap(Collections.singletonList("virtualTeamSize"));
        int virtualTeamSize = 0;
        if (configMap.containsKey("virtualTeamSize")) {
            try {

                virtualTeamSize = NumberUtils.parseNumber(configMap.get("virtualTeamSize"), Integer.class);
            } catch (Exception e) {
                log.error("generateVirtualTeam number format error", e);
                return;
            }
        }

        if (virtualTeamSize > 0) {
            List<GptCar> gptCars = GptCarGenerator.generateGptCars(virtualTeamSize, heatConfigMap.get("high"), "TEAM");
            nodeVirtualTeam = gptCars;
        }
    }

    private void generateVirtualPro() {
        Map<String, String> configMap = chatGptConfigService.getConfigMap(Collections.singletonList("virtualProSize"));
        int virtualProSize = 0;
        if (configMap.containsKey("virtualProSize")) {
            try {

                virtualProSize = NumberUtils.parseNumber(configMap.get("virtualProSize"), Integer.class);
            } catch (Exception e) {
                log.error("generateVirtualPro number format error", e);
                return;
            }
        }

        if (virtualProSize > 0) {
            List<GptCar> gptCars = GptCarGenerator.generateGptCars(virtualProSize, heatConfigMap.get("high"), "PRO");
            nodeVirtualPro = gptCars;
        }
    }

    private void generateVirtualGrok() {
        Map<String, String> configMap = chatGptConfigService.getConfigMap(Collections.singletonList("grokVirtualSize"));
        int virtualSize = 0;
        if (configMap.containsKey("grokVirtualSize")) {
            try {

                virtualSize = localCache.getNumber("grokVirtualSize");
            } catch (Exception e) {
                log.error("generateVirtualGrok number format error", e);
                return;
            }
        }

        if (virtualSize > 0) {
            List<GptCar> gptCars = GptCarGenerator.generateGptCars(virtualSize, heatConfigMap.get("high"), "PRO");
            nodeVirtualGrok = gptCars.stream().map(GptCar::toGrokCar).collect(Collectors.toList());
        }
    }

    private void generateVirtualClaude() {
        Map<String, String> configMap = chatGptConfigService.getConfigMap(Collections.singletonList("claudeVirtualSize"));
        int virtualSize = 0;
        if (configMap.containsKey("claudeVirtualSize")) {
            try {

                virtualSize = localCache.getNumber("claudeVirtualSize");
            } catch (Exception e) {
                log.error("generateVirtualClaude number format error", e);
                return;
            }
        }

        if (virtualSize > 0) {
            List<GptCar> gptCars = GptCarGenerator.generateGptCars(virtualSize, heatConfigMap.get("high"), "PRO");
            nodeVirtualClaude = gptCars.stream().map(GptCar::toClaudeCar).collect(Collectors.toList());
        }
    }

    public Page<ChatGptSession> page(PageQueryReq req) {
        Page<ChatGptSession> page = new Page<>(req.getPageNum(), req.getPageSize());
        if (StringUtils.hasLength(req.getSortField()) && StringUtils.hasLength(req.getSortOrder())) {
            OrderItem orderItem = new OrderItem();
            orderItem.setColumn(req.getSortField());
            orderItem.setAsc(req.getSortOrder().equals("asc"));
            page.addOrder(orderItem);

        }
        QueryWrapper<ChatGptSession> queryWrapper = new QueryWrapper<>();
        queryWrapper.isNull("deleted_at");
        if (StringUtils.hasLength(req.getQuery()))
            queryWrapper.and(wrapper -> wrapper.like("remark", req.getQuery())
                    .or()
                    .like("email", req.getQuery()));
        return chatGptSessionMapper.selectPage(
                page, queryWrapper
        );
    }

    public List<?> carList(String type) {
        List<GptCar> result = Collections.emptyList();
        String carSortRule = localCache.getString("carSortRule","heat");
        if ("free".equals(type)) {
            if (nodeFree != null) {

                result = new ArrayList<>(nodeFree);
            }

        } else if ("4o".equals(type)) {
            if (node4o != null) {

                result = new ArrayList<>(node4o);
            }
        } else if ("plus".equals(type)) {
            result = new ArrayList<>();
            if (nodePlus != null) {

                result.addAll(nodePlus);
            }
            if (nodeTeam != null) {
                result.addAll(nodeTeam);
            }
            if (nodePro != null) {
                result.addAll(nodePro);
            }
            boolean enable4oPlus = localCache.getBoolean("enable4oPlus");
            if (enable4oPlus) {
                result.addAll(node4o);
            }
        } else if ("claude".equals(type)) {
            return new ArrayList<>(nodeClaude);
        } else if ("grok".equals(type)) {
            return new ArrayList<>(nodeGrok);
        }
        if ("free".equals(type) || "4o".equals(type) || "plus".equals(type)) {
            try {
                fillClearsIn((List<GptCar>) result);
            } catch (ExecutionException | InterruptedException e) {
                log.error("fillClearsIn error", e);
            }

            //根据clearsin 和 count 进行排序
            if (!result.isEmpty()) {

                if ("heat".equals(carSortRule)){
                    result.sort(Comparator.comparing(GptCar::getClearsIn).thenComparing(GptCar::getCount));
                }else {
                    result.sort(Comparator.comparing(GptCar::getSort)
                            .thenComparing(GptCar::getClearsIn).thenComparing(GptCar::getCount));
                }
            }
        }

        return result;
    }

    private void fillClearsIn(List<GptCar> carList) throws ExecutionException, InterruptedException {
        if (!CollectionUtils.isEmpty(carList)) {
            List<GptCar> noVirtual = carList.stream().filter(item -> !item.isVirtual()).toList();
            List<String> keys = noVirtual.stream().map(item -> "clears_in:" + item.getCarID()).toList();
            RBatch batch = redissonClient.createBatch();
            Map<String, RFuture<String>> futures = new HashMap<>();

            // 将所有get请求添加到批处理中
            for (String key : keys) {
                RFuture<String> future = batch.<String>getBucket(key, StringCodec.INSTANCE).getAsync();
                futures.put(key, future);
            }

            // 执行批处理
            batch.execute();

            for (GptCar car : noVirtual) {
                RFuture<String> future = futures.get("clears_in:" + car.getCarID());
                String value = future.toCompletableFuture().get();
                if (value != null) {
                    car.setClearsIn(Integer.parseInt(value));
                }
            }
        }
    }

    public void refreshCarLabel() {
        log.info("start refresh carLabel ...");
        if (!CollectionUtils.isEmpty(nodePlus)) {
            for (GptCar plus : nodePlus) {
                fillCarLabel(plus);
            }
        }
        log.info("refresh carLabel done...");
    }

    public void fillCarLabel(GptCar car) {
        String carLabel = chatShareServerProxy.getCarLabel(car.getCarID());

        if (StringUtils.hasLength(carLabel)) {
            car.setLabel(carLabel);
        }
//        car.setLabel("TEAM");
    }

    @Scheduled(fixedRate = 1, timeUnit = TimeUnit.MINUTES)
    public void refreshNode() {
        refreshGptNode();

        boolean enableClaude = Boolean.parseBoolean(localCache.getConfigMap().get("enableClaude"));
        if (enableClaude) {
            refreshClaudeNode();
        }

        boolean enableGrok = Boolean.parseBoolean(localCache.getConfigMap().get("enableGrok"));
        if (enableGrok) {
            refreshGrokNode();
        }
    }

    private void refreshGptNode() {
        log.info("load all car list");
        List<ChatGptSession> carList = loadAll();
        log.info("loaded , car list size:{}", carList.size());


        int nodeFreeSize = 0;
        int virtualPlusSize = 0;
        int virtualTeamSize = 0;
        int virtualProSize = 0;
        Map<String, String> configMap = localCache.getConfigMap();

        try {
            nodeFreeSize = Integer.parseInt(configMap.get("nodeFreeSize"));
            virtualPlusSize = Integer.parseInt(configMap.get("virtualPlusSize"));
            virtualTeamSize = Integer.parseInt(configMap.getOrDefault("virtualTeamSize", "0"));
            virtualProSize = Integer.parseInt(configMap.getOrDefault("virtualProSize", "0"));
        } catch (Exception e) {
            log.error("load config error", e);
        }

        if (!CollectionUtils.isEmpty(carList)) {
            List<ChatGptSession> gpt4o = carList.stream().filter(item -> item.getIsPlus() == 0).toList();
            List<ChatGptSession> gptPlus = carList.stream().filter(item -> item.getIsPlus() == 1).toList();
            if (nodeFreeSize > 0 && !gpt4o.isEmpty()) {
                nodeFree = gpt4o.stream().limit(nodeFreeSize).map(GptCar::fromChatGptSession).toList();
                log.info("nodeFree size:{}", nodeFree.size());
            }
            node4o = gpt4o.stream().map(GptCar::fromChatGptSession).toList();
            log.info("node4o size:{}", node4o.size());

            if (!CollectionUtils.isEmpty(node4o)) {
                String node4oIcon = localCache.getString("node4oIcon","4o");
                if (StringUtils.hasLength(node4oIcon)){
                    node4o.forEach(item -> item.setLabel(node4oIcon));
                }
            }

            nodePlus = gptPlus.stream().map(GptCar::fromChatGptSession).collect(Collectors.toList());
            log.info("nodePlus size:{}", nodePlus.size());


            //填充车队使用次数
            fillCarUseSize(nodePlus);
            fillCarUseSize(node4o);
            fillCarUseSize(nodeFree);

            //填充车队label
            refreshCarLabel();

            //分离 PLUS TEAM PRO
            if (!nodePlus.isEmpty()) {
                nodeTeam = nodePlus.stream().filter(GptCar::isTeam).collect(Collectors.toList());
                nodePro = nodePlus.stream().filter(GptCar::isPro).collect(Collectors.toList());
                nodePlus = nodePlus.stream().filter(e -> !e.isPro() && !e.isTeam()).collect(Collectors.toList());
            }
            if (!nodePlus.isEmpty() && virtualPlusSize > 0 && nodeVirtualPlus != null) {
                //添加虚拟车队
                nodePlus.addAll(nodeVirtualPlus);

            }


            if (!nodeTeam.isEmpty() && virtualTeamSize > 0 && nodeVirtualTeam != null) {
                //添加虚拟车队
                nodeTeam.addAll(nodeVirtualTeam);
            }

            if (!nodePro.isEmpty() && virtualProSize > 0 && nodeVirtualPro != null) {
                //添加虚拟车队
                nodePro.addAll(nodeVirtualPro);
            }


        } else {
            nodeFree = Collections.emptyList();
            node4o = Collections.emptyList();
            nodePlus = new ArrayList<>();
            nodeTeam = new ArrayList<>();
            nodePro = new ArrayList<>();
        }

    }

    public void refreshClaudeNode() {
        String claudeUrl = localCache.getConfigMap().get("claudeUrl");

        if (StringUtils.hasLength(claudeUrl)) {

            List<ClaudeSession> claudeSessions = loadAllClaude();
            if (!CollectionUtils.isEmpty(claudeSessions)) {
                nodeClaude = claudeSessions.stream().map(ClaudeCar::fromClaudeSession).collect(Collectors.toList());
                log.info("nodeClaude size:{}", nodeClaude.size());
            }

            // 添加虚拟车队
            int claudeVirtualSize = localCache.getNumber("claudeVirtualSize");
            if (claudeVirtualSize > 0 && nodeVirtualClaude != null) {
                nodeClaude.addAll(nodeVirtualClaude);
            }
        } else {
            String lyyClaudeUrl = localCache.getConfigMap().get("lyyClaudeUrl");
            if (StringUtils.hasLength(lyyClaudeUrl)) {
                nodeClaude = claudeProxy.carPage();
                log.info("nodeClaude size:{}", nodeClaude.size());

                // 添加虚拟车队
                int claudeVirtualSize = localCache.getNumber("claudeVirtualSize");
                if (claudeVirtualSize > 0 && nodeVirtualClaude != null) {
                    nodeClaude.addAll(nodeVirtualClaude);
                }
            }
        }
    }

    public void refreshGrokNode() {
        String grokUrl = localCache.getConfigMap().get("grokUrl");
        if (StringUtils.hasLength(grokUrl)) {

            // 添加虚拟车队
            int grokVirtualSize = localCache.getNumber("grokVirtualSize");
            if (grokVirtualSize > 0 && nodeVirtualGrok != null) {
                nodeGrok = new ArrayList<>(nodeVirtualGrok);
            }

            log.info("nodeGrok size:{}", nodeGrok.size());
        }
    }

    private void fillCarUseSize(List<GptCar> gptCars) {
        if (!CollectionUtils.isEmpty(gptCars)) {
            List<String> carIds = gptCars.stream().map(GptCar::getCarID).toList();
            Map<String, Integer> accessCountMap;
            try {
                accessCountMap = chatGptSessionAccessComponent.getByCarIds(carIds);

                for (GptCar gptCar : gptCars) {
                    gptCar.setCount(accessCountMap.getOrDefault(gptCar.getCarID(), 0));
                    //0~10 空闲
                    if (gptCar.getCount() < 10) {
                        gptCar.setDesc("空闲");
                    } else if (gptCar.getCount() < 20) {
                        gptCar.setDesc("推荐");
                    } else if (gptCar.getCount() < 30) {
                        gptCar.setDesc("拥挤");
                    } else {
                        gptCar.setDesc("繁忙");
                    }
                }
            } catch (ExecutionException e) {
                log.error("fillCarUseSize error", e);
            }

        }
    }

    private List<ChatGptSession> loadAll() {
        QueryWrapper<ChatGptSession> queryWrapper = new QueryWrapper<>();
        queryWrapper.isNull("deleted_at")
                .eq("status", 1);
        return chatGptSessionMapper.selectList(queryWrapper);
    }

    private List<ClaudeSession> loadAllClaude() {
        QueryWrapper<ClaudeSession> queryWrapper = new QueryWrapper<>();
        return claudeSessionMapper.selectList(queryWrapper);
    }

    public void addGptSessionBatch(List<AddGptSessionReq> list) {

        for (AddGptSessionReq addGptSessionReq : list) {
            threadPoolExecutor.execute(() -> {
                try {

                    String s = chatShareServerProxy.addChatGptSession(addGptSessionReq);
                    log.info("addChatGptSession result:{}", s);
                } catch (Exception e) {
                    log.error("addChatGptSession error", e);
                }
            });
        }
    }

    public boolean addChatGptSession(AddGptSessionReq req) {
        String s = chatShareServerProxy.addChatGptSession(req);
        log.info("addChatGptSession result:{}", s);
        checkShareServerResult(s);

        refreshGptNode();
        return true;
    }

    public boolean updateChatGptSession(AddGptSessionReq req) {
        String s = chatShareServerProxy.updateChatGptSession(req);
        log.info("updateChatGptSession result:{}", s);
        checkShareServerResult(s);

        refreshGptNode();
        return true;
    }

    public boolean deleteChatGptSession(List<Long> ids) {
        String s = chatShareServerProxy.deleteChatGptSession(ids);
        log.info("deleteChatGptSession result:{}", s);
        checkShareServerResult(s);
        refreshGptNode();
        return true;
    }

    private void checkShareServerResult(String result) {
        //{code: 1000, message: "BaseResMessage", data: {Locker: {}}}
        JSONObject jsonObject = JSON.parseObject(result);
        if (jsonObject.getInteger("code") != 1000) {
            throw new FoxException(jsonObject.getString("message"));
        }
    }

    private <T> T randomPickOne(List<T> list) {
        if (list.isEmpty()){
            return null;
        }
        return list.get(new Random().nextInt(list.size()));
    }

    public void checkUserRights(String carId, String type) {
        ChatgptUser user = UserContext.getUser();
        UserGroup group = userGroupService.getById(user.getGroupId());
        LocalDateTime plusExpireTime = user.getPlusExpireTime();
        LocalDateTime now = LocalDateTime.now();
        if (!group.getAvailableNodes().contains(type)) {
            throw new FoxException("car.no.permission", null);
        }
        // 游客不需要校验过期
        if (user.isVisitor()) {
            return;
        }
        if ("plus".equals(type)) {
            if (plusExpireTime.isAfter(now)) {
                return;
            } else {
                throw new FoxException("car.plus.expired", null);
            }
        } else if ("4o".equals(type)) {
            //优先校验plusExpireTime
            if (plusExpireTime.isAfter(now)) {
                return;
            }

            LocalDateTime expireTime = user.getExpireTime();
            if (expireTime.isAfter(now)) {
                return;
            } else {
                throw new FoxException("car.general.expired", null);
            }
        }
    }
    public String selectAnotherCar(String carId, String type) {
        if ("free".equals(type)) {
            if (nodeFree.isEmpty()) {
                return null;
            }
            List<GptCar> list = nodeFree.stream().filter(item -> !item.getCarID().equals(carId))
                    .toList();
            GptCar gptCar = randomPickOne(list);
            if (gptCar!=null){
                return gptCar.getCarID();
            }

        } else if ("4o".equals(type)) {
            if (node4o.isEmpty()) {
                return null;
            }
            List<GptCar> list = node4o.stream().filter(item -> !item.getCarID().equals(carId))
                    .toList();
            GptCar gptCar = randomPickOne(list);
            if (gptCar!=null){
                return gptCar.getCarID();
            }

        } else if ("plus".equals(type)) {
            if (nodePlus.isEmpty() && nodeTeam.isEmpty() && nodePro.isEmpty()) {
                return null;
            }
            List<GptCar> temp = new ArrayList<>();
            temp.addAll(nodePlus);
            temp.addAll(nodeTeam);
            temp.addAll(nodePro);
            List<GptCar> noVirtual = temp.stream().filter(item -> !item.isVirtual()).toList();
            List<GptCar> list = noVirtual.stream().filter(item -> !item.getCarID().equals(carId))
                    .filter(item -> !item.isVirtual())
                    .toList();
            GptCar gptCar = randomPickOne(list);
            if (gptCar!=null){
                return gptCar.getCarID();
            }
        } else if ("claude".equals(type)) {
            if (nodeClaude.isEmpty()) {
                return null;
            }
            List<ClaudeCar> noVirtual = nodeClaude
                    .stream().filter(e -> !e.isVirtual()).toList();
            List<ClaudeCar> list = noVirtual.stream().filter(item -> !item.getCarID().equals(carId))
                    .toList();
            ClaudeCar claudeCar = randomPickOne(list);
            if (claudeCar!=null){
                return claudeCar.getCarID();
            }
        } else if ("grok".equals(type)) {
            if (nodeGrok.isEmpty()) {
                return null;
            }
            List<GrokCar> noVirtual = nodeGrok.stream().filter(item -> !item.isVirtual()).toList();
            List<GrokCar> list = noVirtual.stream().filter(item -> !item.getCarID().equals(carId))
                    .filter(item -> !item.isVirtual())
                    .toList();
            GrokCar grokCar = randomPickOne(list);
            if (grokCar!=null){
                return grokCar.getCarID();
            }
        }
        return null;
    }

    public String selectCarId(String carId, String type) {
        if ("free".equals(type)) {
            if (nodeFree.isEmpty()) {
                return null;
            }
            Optional<GptCar> any = nodeFree.stream().filter(item -> item.getCarID().equals(carId))
                    .findAny();
            if (any.isPresent()) {
                return carId;
            }
            return randomPickOne(nodeFree).getCarID();
        } else if ("4o".equals(type)) {
            if (node4o.isEmpty()) {
                return null;
            }
            return node4o.stream().filter(item -> item.getCarID().equals(carId))
                    .findAny().orElse(randomPickOne(node4o)).getCarID();
        } else if ("plus".equals(type)) {
            if (nodePlus.isEmpty() && nodeTeam.isEmpty() && nodePro.isEmpty()) {
                return null;
            }
            List<GptCar> temp = new ArrayList<>();
            temp.addAll(nodePlus);
            temp.addAll(nodeTeam);
            temp.addAll(nodePro);
            List<GptCar> noVirtual = temp.stream().filter(item -> !item.isVirtual()).toList();
            return noVirtual.stream().filter(item -> item.getCarID().equals(carId))
                    .filter(item -> !item.isVirtual())
                    .findAny().orElse(randomPickOne(noVirtual)).getCarID();
        } else if ("claude".equals(type)) {
            if (nodeClaude.isEmpty()) {
                return null;
            }
            List<ClaudeCar> noVirtual = nodeClaude
                    .stream().filter(e -> !e.isVirtual()).toList();
            return noVirtual.stream().filter(item -> item.getCarID().equals(carId))
                    .findAny().orElse(randomPickOne(noVirtual)).getCarID();
        } else if ("grok".equals(type)) {
            if (nodeGrok.isEmpty()) {
                return null;
            }
            List<GrokCar> noVirtual = nodeGrok.stream().filter(item -> !item.isVirtual()).toList();
            return noVirtual.stream().filter(item -> item.getCarID().equals(carId))
                    .filter(item -> !item.isVirtual())
                    .findAny().orElse(randomPickOne(noVirtual)).getCarID();
        }
        return null;
    }

    public String generateCarId() {
        return GptCarGenerator.generateCarId();
    }


    public synchronized void applyClaudeVirtualSizeChange() {
        int virtualPlusSize = localCache.getNumber("claudeVirtualSize");
        if (virtualPlusSize == 0) {
            nodeVirtualClaude.clear();
            log.info("虚拟Plus数量为0，已清空");
            nodeClaude.removeIf(ClaudeCar::isVirtual);
            return;
        }
        int newVirtualPlusSize = virtualPlusSize - nodeVirtualClaude.size();
        if (newVirtualPlusSize == 0) {

            return;
        }
        if (newVirtualPlusSize > 0) {
            Map<String, String> configMap = localCache.getConfigMap();
            List<ClaudeCar> claudeCars = GptCarGenerator.generateGptCars(newVirtualPlusSize,
                            heatConfigMap.get(configMap.getOrDefault("virtualPlusHeat", "high")), "PRO")
                    .stream().map(GptCar::toClaudeCar).toList();
            nodeVirtualClaude.addAll(claudeCars);
            nodeClaude.removeIf(ClaudeCar::isVirtual);
            nodeClaude.addAll(nodeVirtualClaude);
            log.info("claude虚拟车队数量为{}，已生成{}个", virtualPlusSize, newVirtualPlusSize);
        } else {
            int removeCount = Math.abs(newVirtualPlusSize);
            if (removeCount > nodeVirtualClaude.size()) {
                removeCount = nodeVirtualClaude.size();
            }
            //随机移除
            for (int i = 0; i < removeCount; i++) {
                ClaudeCar gptCar = randomPickOne(nodeVirtualClaude);
                nodeVirtualClaude.remove(gptCar);
            }
            nodeClaude.removeIf(ClaudeCar::isVirtual);
            nodeClaude.addAll(nodeVirtualClaude);
            log.info("claude虚拟车队数量为{}，已移除{}个", virtualPlusSize, removeCount);
        }
    }

    public synchronized void applyGrokVirtualSizeChange() {
        int virtualSize = localCache.getNumber("grokVirtualSize");
        if (virtualSize == 0) {
            nodeVirtualGrok.clear();
            log.info("Grok虚拟车队数量为0，已清空");
            nodeGrok.removeIf(GrokCar::isVirtual);
            return;
        }

        int newVirtualSize = virtualSize - nodeVirtualGrok.size();
        if (newVirtualSize == 0) {
            return;
        }

        if (newVirtualSize > 0) {
            Map<String, String> configMap = localCache.getConfigMap();
            List<GrokCar> grokCars = GptCarGenerator.generateGptCars(newVirtualSize,
                            heatConfigMap.get(configMap.getOrDefault("virtualPlusHeat", "high")), "PRO")
                    .stream().map(GptCar::toGrokCar).toList();
            nodeVirtualGrok.addAll(grokCars);
            nodeGrok.removeIf(GrokCar::isVirtual);
            nodeGrok.addAll(nodeVirtualGrok);
            log.info("Grok虚拟车队数量为{}，已生成{}个", virtualSize, newVirtualSize);
        } else {
            int removeCount = Math.abs(newVirtualSize);
            if (removeCount > nodeVirtualGrok.size()) {
                removeCount = nodeVirtualGrok.size();
            }
            //随机移除
            for (int i = 0; i < removeCount; i++) {
                GrokCar grokCar = randomPickOne(nodeVirtualGrok);
                nodeVirtualGrok.remove(grokCar);
            }
            nodeGrok.removeIf(GrokCar::isVirtual);
            nodeGrok.addAll(nodeVirtualGrok);
            log.info("Grok虚拟车队数量为{}，已移除{}个", virtualSize, removeCount);
        }
    }

    public String selectCarType(String carId) {
        if (nodePlus.stream().anyMatch(item -> item.getCarID().equals(carId))) {
            return "plus";
        } else if (nodeTeam.stream().anyMatch(item -> item.getCarID().equals(carId))) {
            return "plus";
        } else if (nodePro.stream().anyMatch(item -> item.getCarID().equals(carId))) {
            return "plus";
        } else if (node4o.stream().anyMatch(item -> item.getCarID().equals(carId))) {
            return "4o";
        } else if (nodeFree.stream().anyMatch(item -> item.getCarID().equals(carId))) {
            return "free";
        } else if (nodeClaude.stream().anyMatch(item -> item.getCarID().equals(carId))) {
            return "claude";
        } else if (nodeGrok.stream().anyMatch(item -> item.getCarID().equals(carId))) {
            return "grok";
        }
        return null;
    }
}
