package com.kedish.xyhelper_fox.controller;


import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.kedish.xyhelper_fox.cache.LocalCache;
import com.kedish.xyhelper_fox.component.ChatGptSessionAccessComponent;
import com.kedish.xyhelper_fox.component.UserLimitBucketComponent;
import com.kedish.xyhelper_fox.model.resp.FoxResult;
import io.github.bucket4j.*;
import jakarta.annotation.Resource;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.time.Duration;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

import static com.kedish.xyhelper_fox.utils.HttpServletRequestUtils.getGfSessionIdFromCookie;

@RestController
@Slf4j
@RequestMapping("/api")
public class AuditLimitController {

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private LocalCache localCache;

    @Resource
    private UserLimitBucketComponent userLimitBucketComponent;

    @Resource
    private ChatGptSessionAccessComponent chatGptSessionAccessComponent;

    @GetMapping("/testSetSession")
    public FoxResult testSetSession() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("carid", "123");
        jsonObject.put("usertoken", "admin");
        RBucket<Object> bucket = redissonClient.getBucket("gfsession:123", StringCodec.INSTANCE);
        bucket.set(jsonObject.toString());
        bucket.expire(Duration.ofMinutes(5));

        jsonObject = new JSONObject();
        jsonObject.put("carid", "456");
        jsonObject.put("usertoken", "admin");
        bucket = redissonClient.getBucket("gfsession:456", StringCodec.INSTANCE);
        bucket.set(jsonObject.toString());
        bucket.expire(Duration.ofMinutes(5));
        return FoxResult.ok();
    }

    @GetMapping("/checkCarAccess")
    public FoxResult checkCarAccess() {
        try {
            Map<String, Integer> accessCountMap =
                    chatGptSessionAccessComponent.getAccessCountMap(Arrays.asList("123", "456"));

            log.info(JSON.toJSONString(accessCountMap));
        } catch (ExecutionException e) {
            throw new RuntimeException(e);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        return FoxResult.ok();
    }

    @PostMapping("/auditLimit")
    public void auditLimit(@RequestBody JSONObject json, HttpServletRequest httpServletRequest,
                           HttpServletResponse httpServletResponse) throws IOException {
        String authorization = httpServletRequest.getHeader("Authorization");
        String userToken = authorization.replace("Bearer ", "");
//        log.info("auditLimit,json is {}", json.toJSONString());
        // 解析 JSON 字符串

        // 提取 "model" 字段
        String model = json.getString("model");
        String prompt = "";

        if (json.containsKey("messages")) {

            // 提取 "parts" 字段
            prompt = json.getJSONArray("messages")
                    .getJSONObject(0)
                    .getJSONObject("content")
                    .getJSONArray("parts")
                    .getString(0);
            String forbiddenWord = checkForbiddenWord(prompt);
            if (forbiddenWord != null) {
                sendBadRequest(httpServletResponse, String.format("请珍惜账号，不要提问违禁内容【%s】。" +
                        "如果您对违禁内容有疑问，请联系管理员", forbiddenWord));
                return;
            }
        }

        String gfsessionid = getGfSessionIdFromCookie(httpServletRequest);
        RBucket<Object> bucket = redissonClient.getBucket("gfsession:" + gfsessionid, StringCodec.INSTANCE);
        String session = bucket.get().toString();
        JSONObject jsonObject = JSONObject.parseObject(session);
        String usertokenFromSession = jsonObject.getString("usertoken");
        String carid = jsonObject.getString("carid");
        log.info("auditLimit,userToken: {},prompt: {},model: {}, usertokenFromSession:{},carid:{},gfsessionid:{}",
                userToken, prompt, model, usertokenFromSession, carid, gfsessionid);

        UserLimitBucketComponent.RateLimit rateLimit = userLimitBucketComponent.getRateLimit(usertokenFromSession, model);
        if (rateLimit == null) {
            sendBadRequest(httpServletResponse, "您没有访问该模型的权限");
            return;
        }
        VerboseBucket verboseBucket;
        verboseBucket = userLimitBucketComponent.getBucket(usertokenFromSession, rateLimit);


        VerboseResult<ConsumptionProbe> cpr =
                verboseBucket.tryConsumeAndReturnRemaining(rateLimit.getMultiplier());
        BucketConfiguration configuration = cpr.getConfiguration();
        long capacity = Arrays.stream(configuration.getBandwidths())
                .mapToLong(Bandwidth::getCapacity)
                .max().getAsLong();
        VerboseResult.Diagnostics diagnostics = cpr.getDiagnostics();
        long availableTokens = diagnostics.getAvailableTokens();
        long refillingTime = TimeUnit.NANOSECONDS.toSeconds(diagnostics.calculateFullRefillingTime());
        ConsumptionProbe consumptionProbe = cpr.getValue();

        log.info("userToken: {}, capacity: {}, availableTokens: {}, refillingTime: {}, isConsumed: {}",
                usertokenFromSession, capacity, availableTokens, refillingTime, consumptionProbe.isConsumed());
        if (!consumptionProbe.isConsumed()) {
            sendBadRequest(httpServletResponse, String.format("您已达到自身权益的对话速率限制，请等待%s后再试。" +
                    "购买会员可以解锁更高速率", formatTime(refillingTime)));
        } else {
            //记录车队访问
            chatGptSessionAccessComponent.conversation(carid);
            sendSuccess(httpServletResponse);
        }


    }

    @PostMapping("/claude/auditLimit")
    public void claudeAuditLimit(@RequestBody JSONObject json, HttpServletRequest httpServletRequest,
                                 HttpServletResponse httpServletResponse) throws IOException {
        String authorization = httpServletRequest.getHeader("Authorization");
        String userToken = authorization.replace("Bearer ", "");
        log.info("claudeAuditLimit,json is {}", json.toJSONString());
        // 提取 "model" 字段，按照Go代码逻辑处理
        String model = json.getString("model");
        if (model == null || model.isEmpty()) {
            model = "claude-3-7-sonnet";
        } else if ("claude-3-5-haiku-20241022".equals(model)) {
            model = "claude-3-5-haiku";
        }

        // Claude API使用"prompt"字段，不是"messages"
        String prompt = json.getString("prompt");
        if (prompt != null) {
            String forbiddenWord = checkForbiddenWord(prompt);
            if (forbiddenWord != null) {
                sendClaudeBadRequest(httpServletResponse, String.format("请珍惜账号，不要提问违禁内容【%s】。" +
                        "如果您对违禁内容有疑问，请联系管理员", forbiddenWord));
                return;
            }
        }

        String gfsessionid = getGfSessionIdFromCookie(httpServletRequest);
        String usertokenFromSession = userToken;
        String carid = "";
//        RBucket<Object> bucket = redissonClient.getBucket("gfsession:" + gfsessionid, StringCodec.INSTANCE);
//        String session = bucket.get().toString();
//        JSONObject jsonObject = JSONObject.parseObject(session);
//        String usertokenFromSession = jsonObject.getString("usertoken");
//        String carid = jsonObject.getString("carid");
        log.info("claudeAuditLimit,userToken: {},prompt: {},model: {}, usertokenFromSession:{},carid:{},gfsessionid:{}",
                userToken, prompt, model, usertokenFromSession, carid, gfsessionid);

        String claudeModel = model;
        UserLimitBucketComponent.RateLimit rateLimit = userLimitBucketComponent.getRateLimit(usertokenFromSession, claudeModel);
        if (rateLimit == null) {
            sendClaudeBadRequest(httpServletResponse, "您没有访问该模型的权限");
            return;
        }
        VerboseBucket verboseBucket;
        verboseBucket = userLimitBucketComponent.getBucket(usertokenFromSession, rateLimit);

        VerboseResult<ConsumptionProbe> cpr =
                verboseBucket.tryConsumeAndReturnRemaining(rateLimit.getMultiplier());
        BucketConfiguration configuration = cpr.getConfiguration();
        long capacity = Arrays.stream(configuration.getBandwidths())
                .mapToLong(Bandwidth::getCapacity)
                .max().getAsLong();
        VerboseResult.Diagnostics diagnostics = cpr.getDiagnostics();
        long availableTokens = diagnostics.getAvailableTokens();
        long refillingTime = TimeUnit.NANOSECONDS.toSeconds(diagnostics.calculateFullRefillingTime());
        ConsumptionProbe consumptionProbe = cpr.getValue();

        log.info("claudeAuditLimit: userToken: {}, capacity: {}, availableTokens: {}, refillingTime: {}, isConsumed: {}",
                usertokenFromSession, capacity, availableTokens, refillingTime, consumptionProbe.isConsumed());
        if (!consumptionProbe.isConsumed()) {
            sendClaudeRateLimitError(httpServletResponse, String.format("您已达到自身权益的对话速率限制，请等待%s后再试。" +
                    "购买会员可以解锁更高速率", formatTime(refillingTime)));
        } else {
            //记录车队访问
//            chatGptSessionAccessComponent.conversation(carid);
            sendSuccess(httpServletResponse);
        }
    }

    @PostMapping("/grok/auditLimit")
    public void grokAuditLimit(@RequestBody JSONObject json, HttpServletRequest httpServletRequest,
                               HttpServletResponse httpServletResponse) throws IOException {
        String authorization = httpServletRequest.getHeader("Authorization");
        String userToken = authorization.replace("Bearer ", "");
        log.info("grokAuditLimit,json is {}", json.toJSONString());
        // Grok API请求参数解析，按照Go代码逻辑
        String modelName = json.getString("modelName");
        String model;
        if ("grok-latest".equals(modelName)) {
            model = "grok2";
        } else if ("grok-3".equals(modelName)) {
            Boolean isReasoning = json.getBoolean("isReasoning");
            String deepsearchPreset = json.getString("deepsearchPreset");
            if (Boolean.TRUE.equals(isReasoning) && (deepsearchPreset == null || deepsearchPreset.isEmpty())) {
                model = "reasoning";
            } else if (Boolean.FALSE.equals(isReasoning) && "deep".equals(deepsearchPreset)) {
                model = "deepsearch";
            } else if (Boolean.FALSE.equals(isReasoning) && "deeper".equals(deepsearchPreset)) {
                model = "deepersearch";
            } else {
                model = "grok3";
            }
        } else {
            model = modelName != null ? modelName : "grok2";
        }

        String prompt = json.getString("message");
        if (prompt != null) {
            String forbiddenWord = checkForbiddenWord(prompt);
            if (forbiddenWord != null) {
                sendGrokBadRequest(httpServletResponse, String.format("请珍惜账号，不要提问违禁内容【%s】。" +
                        "如果您对违禁内容有疑问，请联系管理员", forbiddenWord));
                return;
            }
        }

        String gfsessionid = getGfSessionIdFromCookie(httpServletRequest);
        String carid = "";
        String usertokenFromSession = userToken;
//        RBucket<Object> bucket = redissonClient.getBucket("gfsession:" + gfsessionid, StringCodec.INSTANCE);
//        String session = bucket.get().toString();
//        JSONObject jsonObject = JSONObject.parseObject(session);
//        String usertokenFromSession = jsonObject.getString("usertoken");
//        String carid = jsonObject.getString("carid");
        log.info("grokAuditLimit,userToken: {},prompt: {},model: {}, usertokenFromSession:{},carid:{},gfsessionid:{}",
                userToken, prompt, model, usertokenFromSession, carid, gfsessionid);

        // 为Grok模型添加前缀，以区分不同系统的模型
        String grokModel = model;
        UserLimitBucketComponent.RateLimit rateLimit = userLimitBucketComponent.getRateLimit(usertokenFromSession, grokModel);
        if (rateLimit == null) {
            sendGrokBadRequest(httpServletResponse, "您没有访问该模型的权限");
            return;
        }
        VerboseBucket verboseBucket;
        verboseBucket = userLimitBucketComponent.getBucket(usertokenFromSession, rateLimit);

        VerboseResult<ConsumptionProbe> cpr =
                verboseBucket.tryConsumeAndReturnRemaining(rateLimit.getMultiplier());
        BucketConfiguration configuration = cpr.getConfiguration();
        long capacity = Arrays.stream(configuration.getBandwidths())
                .mapToLong(Bandwidth::getCapacity)
                .max().getAsLong();
        VerboseResult.Diagnostics diagnostics = cpr.getDiagnostics();
        long availableTokens = diagnostics.getAvailableTokens();
        long refillingTime = TimeUnit.NANOSECONDS.toSeconds(diagnostics.calculateFullRefillingTime());
        ConsumptionProbe consumptionProbe = cpr.getValue();

        log.info("grokAuditLimit, userToken: {}, capacity: {}, availableTokens: {}, refillingTime: {}, isConsumed: {}",
                usertokenFromSession, capacity, availableTokens, refillingTime, consumptionProbe.isConsumed());
        if (!consumptionProbe.isConsumed()) {
            sendGrokRateLimitError(httpServletResponse, String.format("您已达到自身权益的对话速率限制，请等待%s后再试。" +
                    "购买会员可以解锁更高速率", formatTime(refillingTime)));
        } else {
            //记录车队访问
//            chatGptSessionAccessComponent.conversation(carid);
            sendSuccess(httpServletResponse);
        }
    }

    private void sendBadRequest(HttpServletResponse response, String message) throws IOException {
        response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");
        response.getWriter().write(String.format("{\"error\":\"%s\"}", message));
        response.getWriter().flush();
    }

    private void sendSuccess(HttpServletResponse response) throws IOException {
        response.setStatus(HttpServletResponse.SC_OK);
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");
        response.getWriter().write("{\"code\":0}");
        response.getWriter().flush();
    }

    // Claude特定的错误响应格式
    private void sendClaudeBadRequest(HttpServletResponse response, String message) throws IOException {
        response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");
        response.getWriter().write(String.format("{\"type\":\"error\",\"error\":{\"type\":\"blocked content\",\"message\":\"%s\"}}", message));
        response.getWriter().flush();
    }

    private void sendClaudeRateLimitError(HttpServletResponse response, String message) throws IOException {
        response.setStatus(429);
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");
        response.getWriter().write(String.format("{\"type\":\"error\",\"error\":{\"type\":\"rate limit exceeded\",\"message\":\"%s\"}}", message));
        response.getWriter().flush();
    }

    // Grok特定的错误响应格式
    private void sendGrokBadRequest(HttpServletResponse response, String message) throws IOException {
        response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");
        response.getWriter().write(String.format("{\"error\":{\"code\":13,\"message\":\"Don't ask for forbidden content.\",\"detail\":[\"%s\"]}}", message));
        response.getWriter().flush();
    }

    private void sendGrokRateLimitError(HttpServletResponse response, String message) throws IOException {
        response.setStatus(429);
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");
        response.getWriter().write(String.format("{\"error\":{\"code\":13,\"message\":\"rate limit exceeded\",\"detail\":[\"%s\"]}}", message));
        response.getWriter().flush();
    }

    public static String formatTime(long seconds) {
        if (seconds == 0) {
            return "0秒";
        }

        long days = seconds / (24 * 3600);
        seconds %= (24 * 3600);
        long hours = seconds / 3600;
        seconds %= 3600;
        long minutes = seconds / 60;
        seconds %= 60;

        StringBuilder result = new StringBuilder();
        if (days > 0) {
            result.append(days).append("天");
        }
        if (hours > 0) {
            result.append(hours).append("小时");
        }
        if (minutes > 0) {
            result.append(minutes).append("分钟");
        }
        if (seconds > 0) {
            result.append(seconds).append("秒");
        }

        return result.toString().trim(); // 去除末尾多余的空格
    }

    private String checkForbiddenWord(String prompt) {
        List<String> forbiddenWords = localCache.getForbiddenWords();
        if (forbiddenWords.isEmpty()) {
            return null;
        }
        for (String forbiddenWord : forbiddenWords) {
            if (prompt.contains(forbiddenWord)) {
                return forbiddenWord;
            }
        }
        return null;
    }


}
