package com.kedish.xyhelper_fox.controller;

import com.alibaba.fastjson.JSON;
import com.kedish.xyhelper_fox.cache.LocalCache;
import com.kedish.xyhelper_fox.component.UserLimitBucketComponent;
import com.kedish.xyhelper_fox.exception.FoxException;
import com.kedish.xyhelper_fox.model.req.*;
import com.kedish.xyhelper_fox.model.resp.ChatGptUserVO;
import com.kedish.xyhelper_fox.model.resp.FoxPageResult;
import com.kedish.xyhelper_fox.model.resp.FoxResult;
import com.kedish.xyhelper_fox.model.resp.RateLimitVO;
import com.kedish.xyhelper_fox.repo.model.UserRateLimit;
import org.springframework.http.ResponseEntity;
import com.kedish.xyhelper_fox.repo.model.ChatgptUser;
import com.kedish.xyhelper_fox.repo.model.GroupRateLimit;
import com.kedish.xyhelper_fox.security.UserContext;
import com.kedish.xyhelper_fox.service.ChatgptUserService;
import com.kedish.xyhelper_fox.service.EmailService;
import com.kedish.xyhelper_fox.service.UserGroupService;
import com.kedish.xyhelper_fox.utils.CaptchaUtils;
import com.kedish.xyhelper_fox.utils.JwtUtils;
import io.github.bucket4j.VerboseBucket;
import io.github.bucket4j.VerboseResult;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import static com.kedish.xyhelper_fox.security.AuthInterceptor.USER_TOKEN_PREFIX;
import static java.util.Arrays.stream;

;

@RestController
@Slf4j
@RequestMapping("/api/chatGptUser")
public class ChatGptUserController {

    static final String CAPTCHA_KEY = "captcha:";
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private LocalCache localCache;
    @Resource
    private EmailService emailService;

    @Resource
    private ChatgptUserService chatgptUserService;


    @Resource
    private UserLimitBucketComponent userLimitBucketComponent;
    @Autowired
    private UserGroupService userGroupService;

    public static String createCaptchaCode() {
        return String.valueOf((int) ((Math.random() * 9 + 1) * 1000));
    }

    @GetMapping("/getChatGptUser")
    public FoxResult getChatGptUser() {

        ChatgptUser user = UserContext.getUser();
        user.setPassword(null);
        ChatGptUserVO userVO = new ChatGptUserVO();
        BeanUtils.copyProperties(user, userVO);


        if (userVO.getLimit() == null || userVO.getLimit() == 0L) {
            Map<String, String> configMap = localCache.getConfigMap();
            userVO.setLimit(Long.parseLong(configMap.get("useLimit")));
            userVO.setPer(configMap.get("useLimitPer"));
        }
        return FoxResult.ok(userVO);
//        return "hello world";
    }

    @GetMapping("/getRateLimit")
    public FoxResult getRateLimit() {
        ChatgptUser user = UserContext.getUser();
        String userToken = user.getUserToken();

        // Get both group and user-specific rate limits
        List<GroupRateLimit> groupRateLimits = userGroupService.getGroupRateLimit(user.getGroupId());
        List<UserRateLimit> userRateLimits = userGroupService.getUserRateLimit(userToken);

        // Create a map of model -> user rate limit for quick lookup
        Map<String, UserRateLimit> userRateLimitMap = new HashMap<>();
        if (userRateLimits != null && !userRateLimits.isEmpty()) {
            userRateLimitMap = userRateLimits.stream()
                    .filter(e -> e.getRate() > 0)
                    .collect(Collectors.toMap(
                            UserRateLimit::getModel,
                            e -> e,
                            (a, b) -> a
                    ));
        }

        // Create a map to track all models (from both group and user limits)
        Map<String, RateLimitVO> combinedLimits = new HashMap<>();

        // Process group rate limits first
        for (GroupRateLimit groupLimit : groupRateLimits) {
            if (groupLimit.getRate() > 0) {
                String model = groupLimit.getModel();

                // Skip if we already have a user-specific limit for this model
                if (userRateLimitMap.containsKey(model)) {
                    continue;
                }

                RateLimitVO rateLimitVO = new RateLimitVO();
                rateLimitVO.setModel(model);
                rateLimitVO.setLimit(Long.valueOf(groupLimit.getRate()));
                rateLimitVO.setPer(groupLimit.getPeriod());
                rateLimitVO.setMultiplier(groupLimit.getMultiplier());
                rateLimitVO.setSource("group"); // Indicate this is from group limits

                // Get available tokens
                UserLimitBucketComponent.RateLimit rateLimit =
                        userLimitBucketComponent.getRateLimit(groupLimit);
                if (rateLimit != null) {
                    VerboseBucket bucket = userLimitBucketComponent.getBucket(userToken, rateLimit);
                    VerboseResult<Long> availableTokens = bucket.getAvailableTokens();
                    rateLimitVO.setAvailableToken(availableTokens.getValue().intValue());
                }

                combinedLimits.put(model, rateLimitVO);
            }
        }

        // Process user-specific rate limits
        for (UserRateLimit userLimit : userRateLimits) {
            if (userLimit.getRate() > 0) {
                String model = userLimit.getModel();

                RateLimitVO rateLimitVO = new RateLimitVO();
                rateLimitVO.setModel(model);
                rateLimitVO.setLimit(Long.valueOf(userLimit.getRate()));
                rateLimitVO.setPer(userLimit.getPeriod());
                rateLimitVO.setMultiplier(userLimit.getMultiplier());
                rateLimitVO.setSource("user"); // Indicate this is from user-specific limits

                // Get available tokens
                UserLimitBucketComponent.RateLimit rateLimit = new UserLimitBucketComponent.RateLimit(
                        userLimit.getRate(), userLimit.getPeriod(), userLimit.getMultiplier(), model);
                VerboseBucket bucket = userLimitBucketComponent.getBucket(userToken, rateLimit);
                VerboseResult<Long> availableTokens = bucket.getAvailableTokens();
                rateLimitVO.setAvailableToken(availableTokens.getValue().intValue());

                combinedLimits.put(model, rateLimitVO);
            }
        }

        // Convert the map to a list
        List<RateLimitVO> rateLimitVOS = new ArrayList<>(combinedLimits.values());

        return FoxResult.ok(rateLimitVOS);
    }

    @PostMapping("/register")
    public FoxResult register(@RequestBody RegisterReq req) {

        log.info("register param:{}", JSON.toJSONString(req));
        Map<String, String> configMap = localCache.getConfigMap();

        if (!Objects.equals("true", configMap.get("canRegister"))) {
            throw new FoxException("注册功能已关闭");
        }
        chatgptUserService.register(
                req.getUsername(),
                req.getEmail(),
                req.getCaptchaCode(),
                req.getPassword(),
                req.getInviteCode()
        );
        return FoxResult.ok();
    }

    @PostMapping("/forgetPassword")
    public FoxResult forgetPassword(@RequestBody RegisterReq req) {
        log.info("forgetPassword param:{}", JSON.toJSONString(req));
        chatgptUserService.forgetPassword(req);
        return FoxResult.ok();
    }

    @PostMapping("/loginByUserToken")
    public FoxResult loginByUserToken(@RequestBody LoginReq loginReq) {

        log.info("loginByUserToken param:{}", JSON.toJSONString(loginReq));
        String username = loginReq.getUsername();

//        if (!captchaCode.equals(redissonClient.getBucket(CAPTCHA_KEY + imgId).get())) {
//            return FoxResult.fail("验证码错误");
//        }
        ChatgptUser login = chatgptUserService.getUserByUserToken(username);
        if (login == null || login.getEnableUserTokenLogin() == null || !login.getEnableUserTokenLogin()) {
            return FoxResult.fail("授权码不存在");
        }
        String s = JwtUtils.generateToken(login.getUserToken());
        if (!Objects.equals("true", localCache.getConfigMap().get("canLoginMulti"))) {

            RBucket<String> bucket = redissonClient.getBucket(USER_TOKEN_PREFIX + s);
            bucket.set(login.getUserToken(), 86400L * 7, java.util.concurrent.TimeUnit.SECONDS);
        }
        return FoxResult.ok(s);
    }
    @PostMapping("/login")
    public FoxResult login(@RequestBody LoginReq loginReq) {

        log.info("login param:{}", JSON.toJSONString(loginReq));
//        String captchaCode = loginReq.getCaptchaCode();
        String imgId = loginReq.getImgId();
        String username = loginReq.getUsername();
        String password = loginReq.getPassword();

//        if (!captchaCode.equals(redissonClient.getBucket(CAPTCHA_KEY + imgId).get())) {
//            return FoxResult.fail("验证码错误");
//        }
        ChatgptUser login = chatgptUserService.login(username, password);
        if (login == null) {
            return FoxResult.fail("用户名或密码错误");
        }
        String s = JwtUtils.generateToken(login.getUserToken());
        if (!Objects.equals("true", localCache.getConfigMap().get("canLoginMulti"))) {

            RBucket<String> bucket = redissonClient.getBucket(USER_TOKEN_PREFIX + s);
            bucket.set(login.getUserToken(), 86400L * 7, java.util.concurrent.TimeUnit.SECONDS);
        }
        return FoxResult.ok(s);
    }

    @PostMapping("/updatePassword")
    public FoxResult updatePassword(@RequestBody UpdatePasswordReq req) {
        log.info("updatePassword param:{}", JSON.toJSONString(req));

        chatgptUserService.updatePassword(req.getUsername(), req.getOldPassword(), req.getNewPassword());
        return FoxResult.ok();

    }

    @GetMapping("/captcha")
    public FoxResult captcha() throws IOException {
        String captchaCode = createCaptchaCode();
        String imgId = UUID.randomUUID().toString();
        log.info("captcha,captchaCode:{},imgId:{}", captchaCode, imgId);
        redissonClient.getBucket(CAPTCHA_KEY + imgId).set(captchaCode, 600L, java.util.concurrent.TimeUnit.SECONDS);

        BufferedImage captchaImage = CaptchaUtils.createCaptchaImage(captchaCode);
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ImageIO.write(captchaImage, "png", baos);
        byte[] imageBytes = baos.toByteArray();
        String base64Image = Base64.getEncoder().encodeToString(imageBytes);
        return FoxResult.ok(Map.of("imgId", imgId,
                "imgBase64", base64Image)); // 返回 Base64 编码的图片
    }

    @GetMapping("/sendEmailCode")
    public FoxResult sendEmailCode(@RequestParam(value = "email", required = true) String email,
                                   @RequestParam(required = false) String type) {

        log.info("sendEmailCode,email:{}", email);
        emailService.sendCode(email, type == null ? "注册验证码" : type);
        return FoxResult.ok();
    }

    @PostMapping("/testEmailSend")
    public FoxResult testEmailSend() {
        emailService.testSendEmail();
        return FoxResult.ok();
    }

    @PostMapping("/pageUser")
    public FoxPageResult getUserList(@RequestBody PageQueryReq req) {
        log.info("pageUser param:{}", JSON.toJSONString(req));
        return FoxPageResult.fromPage(chatgptUserService.getPageUser(req));
    }

    @PostMapping("/addUser")
    public FoxResult addUser(@RequestBody AddUserReq req) {
        log.info("addUser param:{}", JSON.toJSONString(req));
        chatgptUserService.addOrUpdateUser(req);
        return FoxResult.ok();
    }

    @PostMapping("/deleteUser")
    public FoxResult deleteUser(@RequestParam Long id) {
        log.info("deleteUser param:{}", id);
        chatgptUserService.deleteUser(id);
        return FoxResult.ok();
    }

    @PostMapping("/batchAddUser")
    public FoxResult batchAddUser(@RequestBody BatchAddUserReq req) {
        log.info("batchAddUser param:{}", JSON.toJSONString(req));
        chatgptUserService.batchAddUser(req);
        return FoxResult.ok();
    }

    /**
     * Test endpoint to manually trigger the user expiration check
     * This endpoint is for testing purposes only
     *
     * @return Information about the check results
     */
    @GetMapping("/checkExpiredUsers")
    public ResponseEntity<String> testCheckExpiredUsers() {
        log.info("Manual trigger of user expiration check");

        // Get count of users with groupId not equal to 1 before the check
        int beforeCount = chatgptUserService.getUsersByGroupIdNotEqual(1L).size();

        // Run the check
        int updatedCount = chatgptUserService.checkAndUpdateExpiredUsers();

        // Get count after the check
        int afterCount = chatgptUserService.getUsersByGroupIdNotEqual(1L).size();

        String result = String.format("Check completed. Before: %d users with groupId != 1, " +
                "Updated: %d users to groupId = 1, After: %d users with groupId != 1",
                beforeCount, updatedCount, afterCount);

        log.info(result);
        return ResponseEntity.ok(result);
    }
}
